{"logs": [{"outputFile": "com.example.hotel_reservation_app-mergeDebugResources-41:/values-nl/values-nl.xml", "map": [{"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3637,3830,3931,4042", "endColumns": "102,100,110,98", "endOffsets": "3735,3926,4037,4136"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\2a5e79e83ba32f0781622d43a41ff54b\\transformed\\appcompat-1.3.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,628,748,826,902,994,1088,1183,1277,1377,1471,1567,1662,1754,1846,1928,2039,2142,2241,2356,2470,2573,2728,2831", "endColumns": "117,104,106,84,107,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,623,743,821,897,989,1083,1178,1272,1372,1466,1562,1657,1749,1841,1923,2034,2137,2236,2351,2465,2568,2723,2826,2909"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,628,748,826,902,994,1088,1183,1277,1377,1471,1567,1662,1754,1846,1928,2039,2142,2241,2356,2470,2573,2728,4368", "endColumns": "117,104,106,84,107,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,623,743,821,897,989,1083,1178,1272,1372,1466,1562,1657,1749,1841,1923,2034,2137,2236,2351,2465,2568,2723,2826,4446"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3565,3740,4141,4222,4552,4721,4801", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "3632,3825,4217,4363,4716,4796,4873"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2831,2933,3035,3135,3235,3342,3446,4451", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "2928,3030,3130,3230,3337,3441,3560,4547"}}]}]}