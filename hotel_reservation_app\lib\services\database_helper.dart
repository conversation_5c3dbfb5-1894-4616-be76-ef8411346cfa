import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/reservation.dart';
import '../models/receipt.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('hotel_reservation.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
    );
  }

  Future _createDB(Database db, int version) async {
    const idType = 'INTEGER PRIMARY KEY AUTOINCREMENT';
    const textType = 'TEXT NOT NULL';
    const textTypeNullable = 'TEXT';
    const integerType = 'INTEGER NOT NULL';
    const realType = 'REAL NOT NULL';

    // Users table
    await db.execute('''
      CREATE TABLE ${UserFields.tableName} (
        ${UserFields.id} $idType,
        ${UserFields.email} $textType,
        ${UserFields.password} $textType,
        ${UserFields.firstName} $textType,
        ${UserFields.lastName} $textType,
        ${UserFields.phone} $textTypeNullable,
        ${UserFields.address} $textTypeNullable,
        ${UserFields.dateOfBirth} $textTypeNullable,
        ${UserFields.createdAt} $textType,
        ${UserFields.updatedAt} $textType
      )
    ''');

    // Reservations table
    await db.execute('''
      CREATE TABLE ${ReservationFields.tableName} (
        ${ReservationFields.id} $idType,
        ${ReservationFields.userId} $integerType,
        ${ReservationFields.hotelName} $textType,
        ${ReservationFields.roomType} $textType,
        ${ReservationFields.checkInDate} $textType,
        ${ReservationFields.checkOutDate} $textType,
        ${ReservationFields.numberOfGuests} $integerType,
        ${ReservationFields.totalAmount} $realType,
        ${ReservationFields.status} $textType,
        ${ReservationFields.specialRequests} $textTypeNullable,
        ${ReservationFields.createdAt} $textType,
        ${ReservationFields.updatedAt} $textType,
        FOREIGN KEY (${ReservationFields.userId}) REFERENCES ${UserFields.tableName} (${UserFields.id})
      )
    ''');

    // Receipts table
    await db.execute('''
      CREATE TABLE ${ReceiptFields.tableName} (
        ${ReceiptFields.id} $idType,
        ${ReceiptFields.reservationId} $integerType,
        ${ReceiptFields.receiptNumber} $textType,
        ${ReceiptFields.amount} $realType,
        ${ReceiptFields.paymentMethod} $textType,
        ${ReceiptFields.paymentDate} $textType,
        ${ReceiptFields.description} $textTypeNullable,
        ${ReceiptFields.createdAt} $textType,
        FOREIGN KEY (${ReceiptFields.reservationId}) REFERENCES ${ReservationFields.tableName} (${ReservationFields.id})
      )
    ''');
  }

  // User operations
  Future<User> createUser(User user) async {
    final db = await instance.database;
    final id = await db.insert(UserFields.tableName, user.toJson());
    return user.copy(id: id);
  }

  Future<User?> getUserByEmail(String email) async {
    final db = await instance.database;
    final maps = await db.query(
      UserFields.tableName,
      columns: UserFields.values,
      where: '${UserFields.email} = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return User.fromJson(maps.first);
    } else {
      return null;
    }
  }

  Future<User?> getUserById(int id) async {
    final db = await instance.database;
    final maps = await db.query(
      UserFields.tableName,
      columns: UserFields.values,
      where: '${UserFields.id} = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return User.fromJson(maps.first);
    } else {
      return null;
    }
  }

  Future<int> updateUser(User user) async {
    final db = await instance.database;
    return db.update(
      UserFields.tableName,
      user.toJson(),
      where: '${UserFields.id} = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> deleteUser(int id) async {
    final db = await instance.database;
    return await db.delete(
      UserFields.tableName,
      where: '${UserFields.id} = ?',
      whereArgs: [id],
    );
  }

  // Reservation operations
  Future<Reservation> createReservation(Reservation reservation) async {
    final db = await instance.database;
    final id = await db.insert(ReservationFields.tableName, reservation.toJson());
    return reservation.copy(id: id);
  }

  Future<List<Reservation>> getReservationsByUserId(int userId) async {
    final db = await instance.database;
    final result = await db.query(
      ReservationFields.tableName,
      where: '${ReservationFields.userId} = ?',
      whereArgs: [userId],
      orderBy: '${ReservationFields.createdAt} DESC',
    );

    return result.map((json) => Reservation.fromJson(json)).toList();
  }

  Future<Reservation?> getReservationById(int id) async {
    final db = await instance.database;
    final maps = await db.query(
      ReservationFields.tableName,
      columns: ReservationFields.values,
      where: '${ReservationFields.id} = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Reservation.fromJson(maps.first);
    } else {
      return null;
    }
  }

  Future<int> updateReservation(Reservation reservation) async {
    final db = await instance.database;
    return db.update(
      ReservationFields.tableName,
      reservation.toJson(),
      where: '${ReservationFields.id} = ?',
      whereArgs: [reservation.id],
    );
  }

  Future<int> deleteReservation(int id) async {
    final db = await instance.database;
    return await db.delete(
      ReservationFields.tableName,
      where: '${ReservationFields.id} = ?',
      whereArgs: [id],
    );
  }

  // Receipt operations
  Future<Receipt> createReceipt(Receipt receipt) async {
    final db = await instance.database;
    final id = await db.insert(ReceiptFields.tableName, receipt.toJson());
    return receipt.copy(id: id);
  }

  Future<List<Receipt>> getReceiptsByReservationId(int reservationId) async {
    final db = await instance.database;
    final result = await db.query(
      ReceiptFields.tableName,
      where: '${ReceiptFields.reservationId} = ?',
      whereArgs: [reservationId],
      orderBy: '${ReceiptFields.createdAt} DESC',
    );

    return result.map((json) => Receipt.fromJson(json)).toList();
  }

  Future<List<Receipt>> getAllReceipts() async {
    final db = await instance.database;
    final result = await db.query(
      ReceiptFields.tableName,
      orderBy: '${ReceiptFields.createdAt} DESC',
    );

    return result.map((json) => Receipt.fromJson(json)).toList();
  }

  Future<int> updateReceipt(Receipt receipt) async {
    final db = await instance.database;
    return db.update(
      ReceiptFields.tableName,
      receipt.toJson(),
      where: '${ReceiptFields.id} = ?',
      whereArgs: [receipt.id],
    );
  }

  Future<int> deleteReceipt(int id) async {
    final db = await instance.database;
    return await db.delete(
      ReceiptFields.tableName,
      where: '${ReceiptFields.id} = ?',
      whereArgs: [id],
    );
  }

  Future close() async {
    final db = await instance.database;
    db.close();
  }
}
