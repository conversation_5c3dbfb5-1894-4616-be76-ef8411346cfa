import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user.dart';
import 'database_helper.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyCurrentUser = 'current_user';
  static const String _keyRememberMe = 'remember_me';
  static const String _keySavedEmail = 'saved_email';
  static const String _keySavedPassword = 'saved_password';
  static const String _keyLoginTimestamp = 'login_timestamp';
  static const String _keySessionTimeout = 'session_timeout';

  User? _currentUser;
  bool _isLoggedIn = false;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;

  /// Initialize authentication service and check for existing session
  Future<void> initialize() async {
    await _loadUserSession();
  }

  /// Login user with email and password
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        return AuthResult.failure('Email and password are required');
      }

      // Check user in database
      final user = await DatabaseHelper.instance.getUserByEmail(email.trim());
      
      if (user == null) {
        return AuthResult.failure('User not found');
      }

      if (user.password != password) {
        return AuthResult.failure('Invalid password');
      }

      // Login successful - save session
      await _saveUserSession(user, rememberMe, email, password);
      
      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Login error: $e');
    }
  }

  /// Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
    String? address,
    String? dateOfBirth,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty || firstName.isEmpty || lastName.isEmpty) {
        return AuthResult.failure('All required fields must be filled');
      }

      // Check if user already exists
      final existingUser = await DatabaseHelper.instance.getUserByEmail(email.trim());
      if (existingUser != null) {
        return AuthResult.failure('An account with this email already exists');
      }

      // Create new user
      final user = User(
        email: email.trim(),
        password: password,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        phone: phone?.trim(),
        address: address?.trim(),
        dateOfBirth: dateOfBirth,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final createdUser = await DatabaseHelper.instance.createUser(user);
      
      // Auto-login after registration
      await _saveUserSession(createdUser, false, email, password);
      
      return AuthResult.success(createdUser);
    } catch (e) {
      return AuthResult.failure('Registration error: $e');
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Clear all authentication data
      await prefs.remove(_keyIsLoggedIn);
      await prefs.remove(_keyCurrentUser);
      await prefs.remove(_keyLoginTimestamp);
      await prefs.remove(_keySessionTimeout);
      
      // Only clear saved credentials if "Remember Me" was not checked
      final rememberMe = prefs.getBool(_keyRememberMe) ?? false;
      if (!rememberMe) {
        await prefs.remove(_keySavedEmail);
        await prefs.remove(_keySavedPassword);
        await prefs.remove(_keyRememberMe);
      }

      // Clear in-memory data
      _currentUser = null;
      _isLoggedIn = false;
      
      print('User logged out successfully');
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  /// Check if user session is valid
  Future<bool> isSessionValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;
      
      if (!isLoggedIn) return false;

      // Check session timeout (24 hours default)
      final loginTimestamp = prefs.getInt(_keyLoginTimestamp) ?? 0;
      final sessionTimeout = prefs.getInt(_keySessionTimeout) ?? (24 * 60 * 60 * 1000); // 24 hours in milliseconds
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      
      if (currentTime - loginTimestamp > sessionTimeout) {
        await logout(); // Session expired
        return false;
      }

      return true;
    } catch (e) {
      print('Error checking session validity: $e');
      return false;
    }
  }

  /// Get saved login credentials (if Remember Me was checked)
  Future<Map<String, String?>> getSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(_keyRememberMe) ?? false;
      
      if (rememberMe) {
        return {
          'email': prefs.getString(_keySavedEmail),
          'password': prefs.getString(_keySavedPassword),
        };
      }
      
      return {'email': null, 'password': null};
    } catch (e) {
      print('Error getting saved credentials: $e');
      return {'email': null, 'password': null};
    }
  }

  /// Update current user data
  Future<AuthResult> updateUser(User updatedUser) async {
    try {
      await DatabaseHelper.instance.updateUser(updatedUser);
      
      // Update session data
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyCurrentUser, jsonEncode(updatedUser.toJson()));
      
      _currentUser = updatedUser;
      
      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure('Update error: $e');
    }
  }

  /// Change user password
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('No user logged in');
      }

      if (_currentUser!.password != currentPassword) {
        return AuthResult.failure('Current password is incorrect');
      }

      if (newPassword.length < 6) {
        return AuthResult.failure('New password must be at least 6 characters');
      }

      final updatedUser = _currentUser!.copy(
        password: newPassword,
        updatedAt: DateTime.now(),
      );

      await DatabaseHelper.instance.updateUser(updatedUser);
      
      // Update session data
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyCurrentUser, jsonEncode(updatedUser.toJson()));
      
      // Update saved password if Remember Me is enabled
      final rememberMe = prefs.getBool(_keyRememberMe) ?? false;
      if (rememberMe) {
        await prefs.setString(_keySavedPassword, newPassword);
      }
      
      _currentUser = updatedUser;
      
      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure('Password change error: $e');
    }
  }

  /// Extend session (refresh login timestamp)
  Future<void> extendSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_keyLoginTimestamp, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Error extending session: $e');
    }
  }

  /// Private method to save user session
  Future<void> _saveUserSession(User user, bool rememberMe, String email, String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save session data
      await prefs.setBool(_keyIsLoggedIn, true);
      await prefs.setString(_keyCurrentUser, jsonEncode(user.toJson()));
      await prefs.setInt(_keyLoginTimestamp, DateTime.now().millisecondsSinceEpoch);
      await prefs.setInt(_keySessionTimeout, 24 * 60 * 60 * 1000); // 24 hours
      
      // Save credentials if Remember Me is checked
      await prefs.setBool(_keyRememberMe, rememberMe);
      if (rememberMe) {
        await prefs.setString(_keySavedEmail, email);
        await prefs.setString(_keySavedPassword, password);
      }
      
      // Update in-memory data
      _currentUser = user;
      _isLoggedIn = true;
      
      print('User session saved successfully');
    } catch (e) {
      print('Error saving user session: $e');
    }
  }

  /// Private method to load user session
  Future<void> _loadUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;
      
      if (isLoggedIn && await isSessionValid()) {
        final userJson = prefs.getString(_keyCurrentUser);
        if (userJson != null) {
          final userMap = jsonDecode(userJson) as Map<String, dynamic>;
          _currentUser = User.fromJson(userMap);
          _isLoggedIn = true;
          print('User session loaded successfully');
        }
      } else {
        _isLoggedIn = false;
        _currentUser = null;
      }
    } catch (e) {
      print('Error loading user session: $e');
      _isLoggedIn = false;
      _currentUser = null;
    }
  }

  /// Clear all user data (for app reset)
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      _currentUser = null;
      _isLoggedIn = false;
      print('All user data cleared');
    } catch (e) {
      print('Error clearing user data: $e');
    }
  }
}

/// Authentication result class
class AuthResult {
  final bool isSuccess;
  final String? message;
  final User? user;

  AuthResult._({required this.isSuccess, this.message, this.user});

  factory AuthResult.success(User user) {
    return AuthResult._(isSuccess: true, user: user);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(isSuccess: false, message: message);
  }
}
