{"logs": [{"outputFile": "com.example.hotel_reservation_app-mergeDebugResources-41:/values/values.xml", "map": [{"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\bd5e5c1cb5975bbd2575b94aceb4e7fa\\transformed\\jetified-appcompat-resources-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2239,2255,2261,3553,3569", "startColumns": "4,4,4,4,4", "startOffsets": "142091,142516,142694,187538,187949", "endLines": "2254,2260,2270,3568,3572", "endColumns": "24,24,24,24,24", "endOffsets": "142511,142689,142973,187944,188071"}}, {"source": "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1521,1525", "startColumns": "4,4", "startOffsets": "95887,96068", "endLines": "1524,1527", "endColumns": "12,12", "endOffsets": "96063,96232"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\79275990ee9dddfd68bc7c9d7157e0cd\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "228,229,230,238,239,240,319,3434", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13517,13576,13624,14291,14366,14442,19156,183346", "endLines": "228,229,230,238,239,240,319,3453", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "13571,13619,13675,14361,14437,14509,19217,184136"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "394", "startColumns": "4", "startOffsets": "23576", "endColumns": "82", "endOffsets": "23654"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "357", "startColumns": "4", "startOffsets": "21103", "endColumns": "53", "endOffsets": "21152"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\e60d7cc8f585e105683d15c0883739b4\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "328,356", "startColumns": "4,4", "startOffsets": "19588,21043", "endColumns": "41,59", "endOffsets": "19625,21098"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,314,2218,2224,3514,3522,3537", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,18918,141221,141416,185988,186270,186884", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,314,2223,2228,3521,3536,3552", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,18973,141411,141569,186265,186879,187533"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\f84db7003533a22de0405c5251ecb704\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "114,263,264,265,266,1955,1957,1958,1963,1965", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5700,15925,15978,16031,16084,125297,125473,125595,125857,126052", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "5784,15973,16026,16079,16132,125358,125590,125651,125918,126114"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,112,113,219,220,221,222,223,224,225,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,366,395,396,397,398,399,400,401,413,1950,1951,1956,1959,1964,2109,2110,2770,2787,2957,2990,3020,3053", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2715,2787,4084,4149,5568,5637,12856,12926,12994,13066,13136,13197,13271,14514,14575,14636,14698,14762,14824,14885,14953,15053,15113,15179,15252,15321,15378,15430,16590,16662,16738,16803,16862,16921,16981,17041,17101,17161,17221,17281,17341,17401,17461,17521,17580,17640,17700,17760,17820,17880,17940,18000,18060,18120,18180,18239,18299,18359,18418,18477,18536,18595,18654,19222,19257,19843,19898,19961,20016,20074,20132,20193,20256,20313,20364,20414,20475,20532,20598,20632,20667,21640,23659,23726,23798,23867,23936,24010,24082,24785,124979,125096,125363,125656,125923,137414,137486,159219,159823,167658,169389,170389,171071", "endLines": "29,70,71,88,89,112,113,219,220,221,222,223,224,225,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,366,395,396,397,398,399,400,401,413,1950,1954,1956,1962,1964,2109,2110,2775,2796,2989,3010,3052,3058", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2782,2870,4144,4210,5632,5695,12921,12989,13061,13131,13192,13266,13339,14570,14631,14693,14757,14819,14880,14948,15048,15108,15174,15247,15316,15373,15425,15487,16657,16733,16798,16857,16916,16976,17036,17096,17156,17216,17276,17336,17396,17456,17516,17575,17635,17695,17755,17815,17875,17935,17995,18055,18115,18175,18234,18294,18354,18413,18472,18531,18590,18649,18708,19252,19287,19893,19956,20011,20069,20127,20188,20251,20308,20359,20409,20470,20527,20593,20627,20662,20697,21705,23721,23793,23862,23931,24005,24077,24165,24851,125091,125292,125468,125852,126047,137481,137548,159417,160119,169384,170065,171066,171233"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "315,331,359,3011,3016", "startColumns": "4,4,4,4,4", "startOffsets": "18978,19732,21207,170070,170240", "endLines": "315,331,359,3015,3019", "endColumns": "56,64,63,24,24", "endOffsets": "19030,19792,21266,170235,170384"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "358", "startColumns": "4", "startOffsets": "21157", "endColumns": "49", "endOffsets": "21202"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\f87704cc6ac259b753f491455f413615\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "316,317,322,329,330,349,350,351,352,353", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19035,19075,19292,19630,19685,20702,20756,20808,20857,20918", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19070,19117,19330,19680,19727,20751,20803,20852,20913,20963"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,217,218,403,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3720,3778,3844,3907,12713,12784,24208,24333,24400,24479", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3773,3839,3902,3964,12779,12851,24271,24395,24474,24543"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "21000", "endColumns": "42", "endOffsets": "21038"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\28f988f0d4c2cc22199e4c3cefdd595e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2111,2825,2831", "startColumns": "4,4,4,4", "startOffsets": "164,137553,161481,161692", "endLines": "3,2113,2830,2914", "endColumns": "60,12,24,24", "endOffsets": "220,137693,161687,166203"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\2a5e79e83ba32f0781622d43a41ff54b\\transformed\\appcompat-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,906,932,963,971,977,993,1015,1020,1025,1035,1044,1053,1057,1064,1083,1090,1091,1100,1103,1106,1110,1114,1118,1121,1122,1127,1132,1142,1147,1154,1160,1161,1164,1168,1173,1175,1177,1180,1183,1185,1189,1192,1199,1202,1205,1209,1211,1215,1217,1219,1221,1225,1233,1241,1253,1259,1268,1271,1282,1285,1286,1291,1292,1297,1366,1436,1437,1447,1456,1457,1459,1463,1466,1469,1472,1475,1478,1481,1484,1488,1491,1494,1497,1501,1504,1508,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1534,1536,1537,1538,1539,1540,1541,1542,1543,1545,1546,1548,1549,1551,1553,1554,1556,1557,1558,1559,1560,1561,1563,1564,1565,1566,1567,1568,1570,1572,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1588,1589,1590,1591,1592,1593,1594,1596,1600,1604,1605,1606,1607,1608,1609,1613,1614,1615,1616,1618,1620,1622,1624,1626,1627,1628,1629,1631,1633,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1649,1650,1651,1652,1654,1656,1657,1659,1660,1662,1664,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1679,1680,1681,1682,1684,1685,1686,1687,1688,1690,1692,1694,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1794,1797,1800,1803,1817,1828,1838,1868,1895,1904,1979,2382,2387,2415,2433,2469,2475,2481,2504,2645,2665,2671,2675,2681,2718,2730,2796,2820,2889,2908,2934", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29889,29989,30241,30665,30920,31014,31103,31340,33392,33634,33736,33989,36173,47362,48878,60165,61693,63450,64076,64496,65757,67022,67278,67514,68061,68555,69160,69358,69938,71306,71681,71799,72337,72494,72690,72963,73219,73389,73530,73594,73959,74326,75002,75266,75604,75957,76051,76237,76543,76805,76930,77057,77296,77507,77626,77819,77996,78451,78632,78754,79013,79126,79313,79415,79522,79651,79926,80434,80930,81807,82101,82671,82820,83552,83724,83808,84144,84236,84514,89908,95442,95504,96134,96748,96839,96952,97181,97341,97493,97664,97830,97999,98166,98329,98572,98742,98915,99086,99360,99559,99764,100094,100178,100274,100370,100468,100568,100670,100772,100874,100976,101078,101178,101274,101386,101515,101638,101769,101900,101998,102112,102206,102346,102480,102576,102688,102788,102904,103000,103112,103212,103352,103488,103652,103782,103940,104090,104231,104375,104510,104622,104772,104900,105028,105164,105296,105426,105556,105668,105808,105954,106098,106236,106302,106392,106468,106572,106662,106764,106872,106980,107080,107160,107252,107350,107460,107512,107590,107696,107788,107892,108002,108124,108287,108444,108524,108624,108714,108824,108914,109155,109249,109355,109447,109547,109659,109773,109889,110005,110099,110213,110325,110427,110547,110669,110751,110855,110975,111101,111199,111293,111381,111493,111609,111731,111843,112018,112134,112220,112312,112424,112548,112615,112741,112809,112937,113081,113209,113278,113373,113488,113601,113700,113809,113920,114031,114132,114237,114337,114467,114558,114681,114775,114887,114973,115077,115173,115261,115379,115483,115587,115713,115801,115909,116009,116099,116209,116293,116395,116479,116533,116597,116703,116789,116899,116983,117103,122247,122365,122480,122612,123327,124019,124536,126135,127668,128056,132791,153454,153714,155224,156257,158270,158532,158888,159718,166500,167634,167928,168151,168478,170528,171176,175027,176229,180308,181523,182932", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1793,1796,1799,1802,1816,1827,1837,1867,1894,1903,1978,2381,2386,2414,2432,2468,2474,2480,2503,2644,2664,2670,2674,2680,2717,2729,2795,2819,2888,2907,2933,2942", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29884,29984,30236,30660,30915,31009,31098,31335,33387,33629,33731,33984,36168,47357,48873,60160,61688,63445,64071,64491,65752,67017,67273,67509,68056,68550,69155,69353,69933,71301,71676,71794,72332,72489,72685,72958,73214,73384,73525,73589,73954,74321,74997,75261,75599,75952,76046,76232,76538,76800,76925,77052,77291,77502,77621,77814,77991,78446,78627,78749,79008,79121,79308,79410,79517,79646,79921,80429,80925,81802,82096,82666,82815,83547,83719,83803,84139,84231,84509,89903,95437,95499,96129,96743,96834,96947,97176,97336,97488,97659,97825,97994,98161,98324,98567,98737,98910,99081,99355,99554,99759,100089,100173,100269,100365,100463,100563,100665,100767,100869,100971,101073,101173,101269,101381,101510,101633,101764,101895,101993,102107,102201,102341,102475,102571,102683,102783,102899,102995,103107,103207,103347,103483,103647,103777,103935,104085,104226,104370,104505,104617,104767,104895,105023,105159,105291,105421,105551,105663,105803,105949,106093,106231,106297,106387,106463,106567,106657,106759,106867,106975,107075,107155,107247,107345,107455,107507,107585,107691,107783,107887,107997,108119,108282,108439,108519,108619,108709,108819,108909,109150,109244,109350,109442,109542,109654,109768,109884,110000,110094,110208,110320,110422,110542,110664,110746,110850,110970,111096,111194,111288,111376,111488,111604,111726,111838,112013,112129,112215,112307,112419,112543,112610,112736,112804,112932,113076,113204,113273,113368,113483,113596,113695,113804,113915,114026,114127,114232,114332,114462,114553,114676,114770,114882,114968,115072,115168,115256,115374,115478,115582,115708,115796,115904,116004,116094,116204,116288,116390,116474,116528,116592,116698,116784,116894,116978,117098,122242,122360,122475,122607,123322,124014,124531,126130,127663,128051,132786,153449,153709,155219,156252,158265,158527,158883,159713,166495,167629,167923,168146,168473,170523,171171,175022,176224,180303,181518,182927,183401"}, "to": {"startLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,226,227,231,232,233,234,235,236,237,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,360,361,362,363,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,412,417,418,419,420,421,422,430,431,435,439,443,448,454,461,465,469,474,478,482,486,490,494,498,504,508,514,518,524,528,533,537,540,544,550,554,560,564,570,573,577,581,585,589,593,594,595,596,599,602,605,608,612,613,614,615,616,619,621,623,625,630,631,635,641,645,646,648,660,661,665,671,675,676,677,681,708,712,713,717,745,916,942,1113,1139,1170,1178,1184,1200,1222,1227,1232,1242,1251,1260,1264,1271,1290,1297,1298,1307,1310,1313,1317,1321,1325,1328,1329,1334,1339,1349,1354,1361,1367,1368,1371,1375,1380,1382,1384,1387,1390,1392,1396,1399,1406,1409,1412,1416,1418,1422,1424,1426,1428,1432,1440,1448,1460,1466,1475,1478,1489,1492,1493,1498,1499,1528,1597,1667,1668,1678,1687,1839,1841,1845,1848,1851,1854,1857,1860,1863,1866,1870,1873,1876,1879,1883,1886,1890,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1916,1918,1919,1920,1921,1922,1923,1924,1925,1927,1928,1930,1931,1933,1935,1936,1938,1939,1940,1941,1942,1943,1945,1946,1947,1948,1949,1966,1968,1970,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1986,1987,1988,1989,1990,1991,1992,1994,1998,2002,2003,2004,2005,2006,2007,2011,2012,2013,2014,2016,2018,2020,2022,2024,2025,2026,2027,2029,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2047,2048,2049,2050,2052,2054,2055,2057,2058,2060,2062,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2077,2078,2079,2080,2082,2083,2084,2085,2086,2088,2090,2092,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2114,2189,2192,2195,2198,2212,2229,2271,2300,2327,2336,2398,2766,2797,2935,3059,3083,3089,3105,3126,3250,3278,3284,3428,3454,3502,3573,3673,3693,3748,3760,3786", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2214,2278,2348,2409,2484,2560,2637,2875,2960,3042,3118,3194,3271,3349,3455,3561,3640,3969,4026,4215,4289,4364,4429,4495,4555,4616,4688,4761,4828,4896,4955,5014,5073,5132,5191,5245,5299,5352,5406,5460,5514,5858,5932,6011,6084,6158,6229,6301,6373,6446,6503,6561,6634,6708,6782,6857,6929,7002,7072,7143,7203,7264,7333,7402,7472,7546,7622,7686,7763,7839,7916,7981,8050,8127,8202,8271,8339,8416,8482,8543,8640,8705,8774,8873,8944,9003,9061,9118,9177,9241,9312,9384,9456,9528,9600,9667,9735,9803,9862,9925,9989,10079,10170,10230,10296,10363,10429,10499,10563,10616,10683,10744,10811,10924,10982,11045,11110,11175,11250,11323,11395,11439,11486,11532,11581,11642,11703,11764,11826,11890,11954,12018,12083,12146,12206,12267,12333,12392,12452,12514,12585,12645,13344,13430,13680,13770,13857,13945,14027,14110,14200,16137,16189,16247,16292,16358,16422,16479,16536,18713,18770,18818,18867,19122,19492,19539,19797,20968,21271,21335,21397,21457,21710,21784,21854,21932,21986,22056,22141,22189,22235,22296,22359,22425,22489,22560,22623,22688,22752,22813,22874,22926,22999,23073,23142,23217,23291,23365,23506,24732,25093,25171,25261,25349,25445,25535,26117,26206,26453,26734,26986,27271,27664,28141,28363,28585,28861,29088,29318,29548,29778,30008,30235,30654,30880,31305,31535,31963,32182,32465,32673,32804,33031,33457,33682,34109,34330,34755,34875,35151,35452,35776,36067,36381,36518,36649,36754,36996,37163,37367,37575,37846,37958,38070,38175,38292,38506,38652,38792,38878,39226,39314,39560,39978,40227,40309,40407,41064,41164,41416,41840,42095,42189,42278,42515,44539,44781,44883,45136,47292,57889,59405,70100,71628,73385,74011,74431,75692,76957,77213,77449,77996,78490,79095,79293,79873,81241,81616,81734,82272,82429,82625,82898,83154,83324,83465,83529,83894,84261,84937,85201,85539,85892,85986,86172,86478,86740,86865,86992,87231,87442,87561,87754,87931,88386,88567,88689,88948,89061,89248,89350,89457,89586,89861,90369,90865,91742,92036,92606,92755,93487,93659,93743,94079,94171,96237,101468,106839,106901,107479,108063,116010,116123,116352,116512,116664,116835,117001,117170,117337,117500,117743,117913,118086,118257,118531,118730,118935,119265,119349,119445,119541,119639,119739,119841,119943,120045,120147,120249,120349,120445,120557,120686,120809,120940,121071,121169,121283,121377,121517,121651,121747,121859,121959,122075,122171,122283,122383,122523,122659,122823,122953,123111,123261,123402,123546,123681,123793,123943,124071,124199,124335,124467,124597,124727,124839,126119,126265,126409,126547,126613,126703,126779,126883,126973,127075,127183,127291,127391,127471,127563,127661,127771,127823,127901,128007,128099,128203,128313,128435,128598,128755,128835,128935,129025,129135,129225,129466,129560,129666,129758,129858,129970,130084,130200,130316,130410,130524,130636,130738,130858,130980,131062,131166,131286,131412,131510,131604,131692,131804,131920,132042,132154,132329,132445,132531,132623,132735,132859,132926,133052,133120,133248,133392,133520,133589,133684,133799,133912,134011,134120,134231,134342,134443,134548,134648,134778,134869,134992,135086,135198,135284,135388,135484,135572,135690,135794,135898,136024,136112,136220,136320,136410,136520,136604,136706,136790,136844,136908,137014,137100,137210,137294,137698,140314,140432,140547,140627,140988,141574,142978,144322,145683,146071,148846,159084,160124,166937,171238,171989,172251,172783,173162,177440,178294,178523,183131,184141,185676,188076,192200,192944,195075,195415,196726", "endLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,226,227,231,232,233,234,235,236,237,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,360,361,362,363,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,412,417,418,419,420,421,429,430,434,438,442,447,453,460,464,468,473,477,481,485,489,493,497,503,507,513,517,523,527,532,536,539,543,549,553,559,563,569,572,576,580,584,588,592,593,594,595,598,601,604,607,611,612,613,614,615,618,620,622,624,629,630,634,640,644,645,647,659,660,664,670,674,675,676,680,707,711,712,716,744,915,941,1112,1138,1169,1177,1183,1199,1221,1226,1231,1241,1250,1259,1263,1270,1289,1296,1297,1306,1309,1312,1316,1320,1324,1327,1328,1333,1338,1348,1353,1360,1366,1367,1370,1374,1379,1381,1383,1386,1389,1391,1395,1398,1405,1408,1411,1415,1417,1421,1423,1425,1427,1431,1439,1447,1459,1465,1474,1477,1488,1491,1492,1497,1498,1503,1596,1666,1667,1677,1686,1687,1840,1844,1847,1850,1853,1856,1859,1862,1865,1869,1872,1875,1878,1882,1885,1889,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1915,1917,1918,1919,1920,1921,1922,1923,1924,1926,1927,1929,1930,1932,1934,1935,1937,1938,1939,1940,1941,1942,1944,1945,1946,1947,1948,1949,1967,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1985,1986,1987,1988,1989,1990,1991,1993,1997,2001,2002,2003,2004,2005,2006,2010,2011,2012,2013,2015,2017,2019,2021,2023,2024,2025,2026,2028,2030,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2046,2047,2048,2049,2051,2053,2054,2056,2057,2059,2061,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2076,2077,2078,2079,2081,2082,2083,2084,2085,2087,2089,2091,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2188,2191,2194,2197,2211,2217,2238,2299,2326,2335,2397,2760,2769,2824,2952,3082,3088,3094,3125,3249,3269,3283,3287,3433,3488,3513,3638,3692,3747,3759,3785,3792", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2136,2273,2343,2404,2479,2555,2632,2710,2955,3037,3113,3189,3266,3344,3450,3556,3635,3715,4021,4079,4284,4359,4424,4490,4550,4611,4683,4756,4823,4891,4950,5009,5068,5127,5186,5240,5294,5347,5401,5455,5509,5563,5927,6006,6079,6153,6224,6296,6368,6441,6498,6556,6629,6703,6777,6852,6924,6997,7067,7138,7198,7259,7328,7397,7467,7541,7617,7681,7758,7834,7911,7976,8045,8122,8197,8266,8334,8411,8477,8538,8635,8700,8769,8868,8939,8998,9056,9113,9172,9236,9307,9379,9451,9523,9595,9662,9730,9798,9857,9920,9984,10074,10165,10225,10291,10358,10424,10494,10558,10611,10678,10739,10806,10919,10977,11040,11105,11170,11245,11318,11390,11434,11481,11527,11576,11637,11698,11759,11821,11885,11949,12013,12078,12141,12201,12262,12328,12387,12447,12509,12580,12640,12708,13425,13512,13765,13852,13940,14022,14105,14195,14286,16184,16242,16287,16353,16417,16474,16531,16585,18765,18813,18862,18913,19151,19534,19583,19838,20995,21330,21392,21452,21509,21779,21849,21927,21981,22051,22136,22184,22230,22291,22354,22420,22484,22555,22618,22683,22747,22808,22869,22921,22994,23068,23137,23212,23286,23360,23501,23571,24780,25166,25256,25344,25440,25530,26112,26201,26448,26729,26981,27266,27659,28136,28358,28580,28856,29083,29313,29543,29773,30003,30230,30649,30875,31300,31530,31958,32177,32460,32668,32799,33026,33452,33677,34104,34325,34750,34870,35146,35447,35771,36062,36376,36513,36644,36749,36991,37158,37362,37570,37841,37953,38065,38170,38287,38501,38647,38787,38873,39221,39309,39555,39973,40222,40304,40402,41059,41159,41411,41835,42090,42184,42273,42510,44534,44776,44878,45131,47287,57884,59400,70095,71623,73380,74006,74426,75687,76952,77208,77444,77991,78485,79090,79288,79868,81236,81611,81729,82267,82424,82620,82893,83149,83319,83460,83524,83889,84256,84932,85196,85534,85887,85981,86167,86473,86735,86860,86987,87226,87437,87556,87749,87926,88381,88562,88684,88943,89056,89243,89345,89452,89581,89856,90364,90860,91737,92031,92601,92750,93482,93654,93738,94074,94166,94444,101463,106834,106896,107474,108058,108149,116118,116347,116507,116659,116830,116996,117165,117332,117495,117738,117908,118081,118252,118526,118725,118930,119260,119344,119440,119536,119634,119734,119836,119938,120040,120142,120244,120344,120440,120552,120681,120804,120935,121066,121164,121278,121372,121512,121646,121742,121854,121954,122070,122166,122278,122378,122518,122654,122818,122948,123106,123256,123397,123541,123676,123788,123938,124066,124194,124330,124462,124592,124722,124834,124974,126260,126404,126542,126608,126698,126774,126878,126968,127070,127178,127286,127386,127466,127558,127656,127766,127818,127896,128002,128094,128198,128308,128430,128593,128750,128830,128930,129020,129130,129220,129461,129555,129661,129753,129853,129965,130079,130195,130311,130405,130519,130631,130733,130853,130975,131057,131161,131281,131407,131505,131599,131687,131799,131915,132037,132149,132324,132440,132526,132618,132730,132854,132921,133047,133115,133243,133387,133515,133584,133679,133794,133907,134006,134115,134226,134337,134438,134543,134643,134773,134864,134987,135081,135193,135279,135383,135479,135567,135685,135789,135893,136019,136107,136215,136315,136405,136515,136599,136701,136785,136839,136903,137009,137095,137205,137289,137409,140309,140427,140542,140622,140983,141216,142086,144317,145678,146066,148841,158894,159214,161476,167504,171984,172246,172446,173157,177435,178041,178518,178669,183341,185219,185983,191097,192939,195070,195410,196721,196924"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "62,115,256,257,258,259,260,261,262,323,324,325,364,365,402,404,408,409,414,415,416,1504,1688,1691,1697,1703,1706,1712,1716,1719,1726,1732,1735,1741,1746,1751,1758,1760,1766,1772,1780,1785,1792,1797,1803,1807,1814,1818,1824,1830,1833,1837,1838,2761,2776,2915,2953,3095,3270,3288,3352,3362,3372,3379,3385,3489,3639,3656", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2141,5789,15492,15556,15611,15679,15746,15811,15868,19335,19383,19431,21514,21577,24170,24276,24548,24592,24856,24995,25045,94449,108154,108259,108504,108842,108988,109328,109540,109703,110110,110448,110571,110910,111149,111406,111777,111837,112175,112461,112910,113202,113590,113895,114239,114484,114814,115021,115289,115562,115706,115907,115954,158899,159422,166208,167509,172451,178046,178674,180599,180881,181186,181448,181708,185224,191102,191632", "endLines": "62,115,256,257,258,259,260,261,262,323,324,325,364,365,402,404,408,411,414,415,416,1520,1690,1696,1702,1705,1711,1715,1718,1725,1731,1734,1740,1745,1750,1757,1759,1765,1771,1779,1784,1791,1796,1802,1806,1813,1817,1823,1829,1832,1836,1837,1838,2765,2786,2934,2956,3104,3277,3351,3361,3371,3378,3384,3427,3501,3655,3672", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2209,5853,15551,15606,15674,15741,15806,15863,15920,19378,19426,19487,21572,21635,24203,24328,24587,24727,24990,25040,25088,95882,108254,108499,108837,108983,109323,109535,109698,110105,110443,110566,110905,111144,111401,111772,111832,112170,112456,112905,113197,113585,113890,114234,114479,114809,115016,115284,115557,115701,115902,115949,116005,159079,159818,166932,167653,172778,178289,180594,180876,181181,181443,181703,183126,185671,191627,192195"}}]}]}