class Hotel {
  final int id;
  final String name;
  final String description;
  final String address;
  final String city;
  final String country;
  final double rating;
  final int reviewCount;
  final double pricePerNight;
  final String imageUrl;
  final List<String> amenities;
  final String phoneNumber;
  final String email;
  final String website;

  const Hotel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.country,
    required this.rating,
    required this.reviewCount,
    required this.pricePerNight,
    required this.imageUrl,
    required this.amenities,
    required this.phoneNumber,
    required this.email,
    required this.website,
  });

  factory Hotel.fromJson(Map<String, dynamic> json) {
    return Hotel(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      pricePerNight: (json['pricePerNight'] as num).toDouble(),
      imageUrl: json['imageUrl'] as String,
      amenities: List<String>.from(json['amenities'] as List),
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      website: json['website'] as String,
    );
  }

  factory Hotel.fromPlaceholderData(Map<String, dynamic> placeholderData) {
    final id = placeholderData['id'] as int;
    final title = placeholderData['title'] as String;
    final body = placeholderData['body'] as String;

    // Generate hotel data based on placeholder data
    final hotelNames = [
      'Grand Palace Hotel',
      'Luxury Resort & Spa',
      'Downtown Business Hotel',
      'Seaside Paradise Resort',
      'Mountain View Lodge',
      'City Center Suites',
      'Royal Garden Hotel',
      'Boutique Inn',
      'Executive Tower',
      'Coastal Retreat',
    ];

    final cities = [
      'New York',
      'Los Angeles',
      'Chicago',
      'Miami',
      'San Francisco',
      'Las Vegas',
      'Boston',
      'Seattle',
      'Denver',
      'Austin',
    ];

    final countries = [
      'United States',
      'Canada',
      'United Kingdom',
      'France',
      'Germany',
      'Italy',
      'Spain',
      'Australia',
      'Japan',
      'Brazil',
    ];

    final amenitiesList = [
      ['Free WiFi', 'Swimming Pool', 'Fitness Center', 'Restaurant'],
      ['Spa Services', 'Room Service', 'Business Center', 'Parking'],
      ['Concierge', 'Laundry Service', 'Airport Shuttle', 'Pet Friendly'],
      ['Bar/Lounge', 'Meeting Rooms', 'Tennis Court', 'Golf Course'],
      ['Beach Access', 'Water Sports', 'Kids Club', 'Entertainment'],
    ];

    final nameIndex = id % hotelNames.length;
    final cityIndex = id % cities.length;
    final countryIndex = id % countries.length;
    final amenitiesIndex = id % amenitiesList.length;

    return Hotel(
      id: id,
      name: hotelNames[nameIndex],
      description: _generateDescription(title, body),
      address: '${100 + id} Main Street',
      city: cities[cityIndex],
      country: countries[countryIndex],
      rating: 3.0 + (id % 3) * 0.5 + ((id % 10) * 0.1), // 3.0-5.0
      reviewCount: 50 + (id * 23) % 500, // 50-550 reviews
      pricePerNight: 80.0 + (id % 15) * 20.0, // $80-$380 per night
      imageUrl: 'https://picsum.photos/400/300?random=$id',
      amenities: amenitiesList[amenitiesIndex],
      phoneNumber: '+1 (555) ${100 + id}-${1000 + (id * 7) % 9000}',
      email: 'info@${hotelNames[nameIndex].toLowerCase().replaceAll(' ', '').replaceAll('&', '')}.com',
      website: 'www.${hotelNames[nameIndex].toLowerCase().replaceAll(' ', '').replaceAll('&', '')}.com',
    );
  }

  static String _generateDescription(String title, String body) {
    final descriptions = [
      'Experience luxury and comfort in the heart of the city. Our hotel offers world-class amenities and exceptional service.',
      'Discover the perfect blend of modern elegance and timeless charm. Ideal for both business and leisure travelers.',
      'Enjoy breathtaking views and premium accommodations. Our dedicated staff ensures an unforgettable stay.',
      'Indulge in sophisticated comfort with state-of-the-art facilities and personalized service.',
      'Escape to a tranquil oasis featuring exquisite dining, relaxing spa services, and luxurious rooms.',
      'Experience unparalleled hospitality in our beautifully appointed hotel with stunning architecture.',
      'Relax in style with our premium amenities, gourmet dining options, and convenient location.',
      'Discover exceptional value with comfortable accommodations and friendly, professional service.',
      'Enjoy a memorable stay with our elegant rooms, fine dining, and comprehensive business facilities.',
      'Immerse yourself in luxury with our award-winning service and world-class accommodations.',
    ];

    final hash = (title + body).hashCode.abs();
    return descriptions[hash % descriptions.length];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'country': country,
      'rating': rating,
      'reviewCount': reviewCount,
      'pricePerNight': pricePerNight,
      'imageUrl': imageUrl,
      'amenities': amenities,
      'phoneNumber': phoneNumber,
      'email': email,
      'website': website,
    };
  }

  String get fullAddress => '$address, $city, $country';

  String get formattedPrice => '\$${pricePerNight.toStringAsFixed(0)}';

  String get formattedRating => rating.toStringAsFixed(1);

  bool hasAmenity(String amenity) {
    return amenities.any((a) => a.toLowerCase().contains(amenity.toLowerCase()));
  }

  @override
  String toString() {
    return 'Hotel{id: $id, name: $name, city: $city, rating: $rating, pricePerNight: $pricePerNight}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Hotel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
