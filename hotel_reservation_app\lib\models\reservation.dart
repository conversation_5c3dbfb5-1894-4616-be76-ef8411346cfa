class ReservationFields {
  static final List<String> values = [
    id, userId, hotelName, roomType, checkInDate, checkOutDate, 
    numberOfGuests, totalAmount, status, specialRequests, createdAt, updatedAt
  ];

  static const String tableName = 'reservations';
  static const String id = '_id';
  static const String userId = 'user_id';
  static const String hotelName = 'hotel_name';
  static const String roomType = 'room_type';
  static const String checkInDate = 'check_in_date';
  static const String checkOutDate = 'check_out_date';
  static const String numberOfGuests = 'number_of_guests';
  static const String totalAmount = 'total_amount';
  static const String status = 'status';
  static const String specialRequests = 'special_requests';
  static const String createdAt = 'created_at';
  static const String updatedAt = 'updated_at';
}

enum ReservationStatus {
  pending,
  confirmed,
  checkedIn,
  checkedOut,
  cancelled,
}

extension ReservationStatusExtension on ReservationStatus {
  String get displayName {
    switch (this) {
      case ReservationStatus.pending:
        return 'Pending';
      case ReservationStatus.confirmed:
        return 'Confirmed';
      case ReservationStatus.checkedIn:
        return 'Checked In';
      case ReservationStatus.checkedOut:
        return 'Checked Out';
      case ReservationStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value {
    switch (this) {
      case ReservationStatus.pending:
        return 'pending';
      case ReservationStatus.confirmed:
        return 'confirmed';
      case ReservationStatus.checkedIn:
        return 'checked_in';
      case ReservationStatus.checkedOut:
        return 'checked_out';
      case ReservationStatus.cancelled:
        return 'cancelled';
    }
  }

  static ReservationStatus fromString(String status) {
    switch (status) {
      case 'pending':
        return ReservationStatus.pending;
      case 'confirmed':
        return ReservationStatus.confirmed;
      case 'checked_in':
        return ReservationStatus.checkedIn;
      case 'checked_out':
        return ReservationStatus.checkedOut;
      case 'cancelled':
        return ReservationStatus.cancelled;
      default:
        return ReservationStatus.pending;
    }
  }
}

class Reservation {
  final int? id;
  final int userId;
  final String hotelName;
  final String roomType;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final int numberOfGuests;
  final double totalAmount;
  final ReservationStatus status;
  final String? specialRequests;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Reservation({
    this.id,
    required this.userId,
    required this.hotelName,
    required this.roomType,
    required this.checkInDate,
    required this.checkOutDate,
    required this.numberOfGuests,
    required this.totalAmount,
    required this.status,
    this.specialRequests,
    required this.createdAt,
    required this.updatedAt,
  });

  Reservation copy({
    int? id,
    int? userId,
    String? hotelName,
    String? roomType,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? numberOfGuests,
    double? totalAmount,
    ReservationStatus? status,
    String? specialRequests,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      Reservation(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        hotelName: hotelName ?? this.hotelName,
        roomType: roomType ?? this.roomType,
        checkInDate: checkInDate ?? this.checkInDate,
        checkOutDate: checkOutDate ?? this.checkOutDate,
        numberOfGuests: numberOfGuests ?? this.numberOfGuests,
        totalAmount: totalAmount ?? this.totalAmount,
        status: status ?? this.status,
        specialRequests: specialRequests ?? this.specialRequests,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  static Reservation fromJson(Map<String, Object?> json) => Reservation(
        id: json[ReservationFields.id] as int?,
        userId: json[ReservationFields.userId] as int,
        hotelName: json[ReservationFields.hotelName] as String,
        roomType: json[ReservationFields.roomType] as String,
        checkInDate: DateTime.parse(json[ReservationFields.checkInDate] as String),
        checkOutDate: DateTime.parse(json[ReservationFields.checkOutDate] as String),
        numberOfGuests: json[ReservationFields.numberOfGuests] as int,
        totalAmount: json[ReservationFields.totalAmount] as double,
        status: ReservationStatusExtension.fromString(json[ReservationFields.status] as String),
        specialRequests: json[ReservationFields.specialRequests] as String?,
        createdAt: DateTime.parse(json[ReservationFields.createdAt] as String),
        updatedAt: DateTime.parse(json[ReservationFields.updatedAt] as String),
      );

  Map<String, Object?> toJson() => {
        ReservationFields.id: id,
        ReservationFields.userId: userId,
        ReservationFields.hotelName: hotelName,
        ReservationFields.roomType: roomType,
        ReservationFields.checkInDate: checkInDate.toIso8601String(),
        ReservationFields.checkOutDate: checkOutDate.toIso8601String(),
        ReservationFields.numberOfGuests: numberOfGuests,
        ReservationFields.totalAmount: totalAmount,
        ReservationFields.status: status.value,
        ReservationFields.specialRequests: specialRequests,
        ReservationFields.createdAt: createdAt.toIso8601String(),
        ReservationFields.updatedAt: updatedAt.toIso8601String(),
      };

  int get numberOfNights {
    return checkOutDate.difference(checkInDate).inDays;
  }

  @override
  String toString() {
    return 'Reservation{id: $id, userId: $userId, hotelName: $hotelName, roomType: $roomType, checkInDate: $checkInDate, checkOutDate: $checkOutDate, numberOfGuests: $numberOfGuests, totalAmount: $totalAmount, status: $status, specialRequests: $specialRequests, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Reservation &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
