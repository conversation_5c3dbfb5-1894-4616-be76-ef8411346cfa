import 'dart:async';
import 'dart:convert';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';

class BluetoothService {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  BluetoothConnection? _connection;
  List<BluetoothDevice> _devicesList = [];
  bool _isConnecting = false;
  bool _isEnabled = false;

  List<BluetoothDevice> get devicesList => _devicesList;
  bool get isConnecting => _isConnecting;
  bool get isEnabled => _isEnabled;
  bool get isConnected => _connection?.isConnected ?? false;

  Future<bool> initialize() async {
    try {
      // Check if Bluetooth is available
      _isEnabled = await FlutterBluetoothSerial.instance.isEnabled ?? false;
      return true;
    } catch (e) {
      debugPrint('Error initializing Bluetooth: $e');
      return false;
    }
  }

  Future<bool> requestPermissions() async {
    try {
      final Map<Permission, PermissionStatus> statuses = await [
        Permission.bluetooth,
        Permission.bluetoothConnect,
        Permission.bluetoothScan,
        Permission.location,
      ].request();

      return statuses.values.every((status) => status.isGranted);
    } catch (e) {
      debugPrint('Error requesting Bluetooth permissions: $e');
      return false;
    }
  }

  Future<bool> enableBluetooth() async {
    try {
      if (!await requestPermissions()) {
        return false;
      }

      final result = await FlutterBluetoothSerial.instance.requestEnable();
      _isEnabled = result ?? false;
      return _isEnabled;
    } catch (e) {
      debugPrint('Error enabling Bluetooth: $e');
      return false;
    }
  }

  Future<bool> disableBluetooth() async {
    try {
      await disconnect();
      final result = await FlutterBluetoothSerial.instance.requestDisable();
      _isEnabled = !(result ?? true);
      return !_isEnabled;
    } catch (e) {
      debugPrint('Error disabling Bluetooth: $e');
      return false;
    }
  }

  Future<List<BluetoothDevice>> scanForDevices() async {
    try {
      if (!_isEnabled) {
        throw Exception('Bluetooth is not enabled');
      }

      if (!await requestPermissions()) {
        throw Exception('Bluetooth permissions not granted');
      }

      // Get bonded devices
      final bondedDevices = await FlutterBluetoothSerial.instance.getBondedDevices();
      _devicesList = bondedDevices;

      return _devicesList;
    } catch (e) {
      debugPrint('Error scanning for devices: $e');
      return [];
    }
  }

  Future<bool> connectToDevice(BluetoothDevice device) async {
    try {
      if (_isConnecting) return false;

      _isConnecting = true;
      
      if (_connection?.isConnected ?? false) {
        await disconnect();
      }

      _connection = await BluetoothConnection.toAddress(device.address);
      _isConnecting = false;

      if (_connection?.isConnected ?? false) {
        debugPrint('Connected to ${device.name}');
        return true;
      } else {
        debugPrint('Failed to connect to ${device.name}');
        return false;
      }
    } catch (e) {
      _isConnecting = false;
      debugPrint('Error connecting to device: $e');
      return false;
    }
  }

  Future<void> disconnect() async {
    try {
      if (_connection?.isConnected ?? false) {
        await _connection?.close();
        _connection = null;
        debugPrint('Bluetooth disconnected');
      }
    } catch (e) {
      debugPrint('Error disconnecting Bluetooth: $e');
    }
  }

  Future<bool> sendData(String data) async {
    try {
      if (!(_connection?.isConnected ?? false)) {
        throw Exception('No active Bluetooth connection');
      }

      _connection?.output.add(utf8.encode(data));
      await _connection?.output.allSent;
      debugPrint('Data sent: $data');
      return true;
    } catch (e) {
      debugPrint('Error sending data: $e');
      return false;
    }
  }

  Stream<String>? getDataStream() {
    if (!(_connection?.isConnected ?? false)) {
      return null;
    }

    return _connection?.input?.transform(utf8.decoder).map((data) {
      debugPrint('Data received: $data');
      return data;
    });
  }

  Future<bool> sendReservationData(Map<String, dynamic> reservationData) async {
    try {
      final jsonData = jsonEncode(reservationData);
      return await sendData(jsonData);
    } catch (e) {
      debugPrint('Error sending reservation data: $e');
      return false;
    }
  }

  Future<bool> shareHotelInfo(Map<String, dynamic> hotelInfo) async {
    try {
      final jsonData = jsonEncode({
        'type': 'hotel_info',
        'data': hotelInfo,
        'timestamp': DateTime.now().toIso8601String(),
      });
      return await sendData(jsonData);
    } catch (e) {
      debugPrint('Error sharing hotel info: $e');
      return false;
    }
  }

  void dispose() {
    disconnect();
  }
}
