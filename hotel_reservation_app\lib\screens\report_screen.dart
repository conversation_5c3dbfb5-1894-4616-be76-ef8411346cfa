import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/app_theme.dart';
import '../models/user.dart';
import '../models/reservation.dart';
import '../models/receipt.dart';
import '../services/database_helper.dart';
import '../services/notification_service.dart';

class ReportScreen extends StatefulWidget {
  final User user;

  const ReportScreen({super.key, required this.user});

  @override
  State<ReportScreen> createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  List<Reservation> _reservations = [];
  List<Receipt> _receipts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reservations = await DatabaseHelper.instance.getReservationsByUserId(widget.user.id!);
      final receipts = await DatabaseHelper.instance.getAllReceipts();

      setState(() {
        _reservations = reservations;
        _receipts = receipts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _generateReceipt(Reservation reservation) async {
    try {
      final receipt = Receipt(
        reservationId: reservation.id!,
        receiptNumber: 'RCP-${DateTime.now().millisecondsSinceEpoch}',
        amount: reservation.totalAmount,
        paymentMethod: PaymentMethod.creditCard,
        paymentDate: DateTime.now(),
        description: 'Payment for ${reservation.hotelName} - ${reservation.roomType}',
        createdAt: DateTime.now(),
      );

      final createdReceipt = await DatabaseHelper.instance.createReceipt(receipt);
      await _loadData(); // Refresh data

      // Send payment confirmation notification
      await NotificationService().showPaymentConfirmation(
        receiptNumber: receipt.receiptNumber,
        amount: receipt.amount,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Receipt generated successfully! Notification sent.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating receipt: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.cream,
      appBar: AppBar(
        title: Text(
          'Reports',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.primaryGold,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withOpacity(0.7),
          labelStyle: GoogleFonts.lato(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          tabs: const [
            Tab(text: 'Reservations'),
            Tab(text: 'Receipts'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGold),
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildReservationsTab(),
                _buildReceiptsTab(),
              ],
            ),
    );
  }

  Widget _buildReservationsTab() {
    if (_reservations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.hotel_outlined,
              size: 80,
              color: AppTheme.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No Reservations Yet',
              style: GoogleFonts.playfairDisplay(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppTheme.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your booking history will appear here',
              style: GoogleFonts.lato(
                fontSize: 16,
                color: AppTheme.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      color: AppTheme.primaryGold,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _reservations.length,
        itemBuilder: (context, index) {
          final reservation = _reservations[index];
          return _buildReservationCard(reservation);
        },
      ),
    );
  }

  Widget _buildReceiptsTab() {
    if (_receipts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.receipt_outlined,
              size: 80,
              color: AppTheme.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No Receipts Yet',
              style: GoogleFonts.playfairDisplay(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppTheme.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your payment receipts will appear here',
              style: GoogleFonts.lato(
                fontSize: 16,
                color: AppTheme.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      color: AppTheme.primaryGold,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _receipts.length,
        itemBuilder: (context, index) {
          final receipt = _receipts[index];
          return _buildReceiptCard(receipt);
        },
      ),
    );
  }

  Widget _buildReservationCard(Reservation reservation) {
    Color statusColor;
    switch (reservation.status) {
      case ReservationStatus.confirmed:
        statusColor = Colors.green;
        break;
      case ReservationStatus.pending:
        statusColor = Colors.orange;
        break;
      case ReservationStatus.cancelled:
        statusColor = Colors.red;
        break;
      case ReservationStatus.checkedIn:
        statusColor = Colors.blue;
        break;
      case ReservationStatus.checkedOut:
        statusColor = AppTheme.grey;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    reservation.hotelName,
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.deepBlue,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    reservation.status.displayName,
                    style: GoogleFonts.lato(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: statusColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildReservationDetail(Icons.bed, 'Room Type', reservation.roomType),
            _buildReservationDetail(Icons.calendar_today, 'Check-in', 
                '${reservation.checkInDate.day}/${reservation.checkInDate.month}/${reservation.checkInDate.year}'),
            _buildReservationDetail(Icons.calendar_today, 'Check-out', 
                '${reservation.checkOutDate.day}/${reservation.checkOutDate.month}/${reservation.checkOutDate.year}'),
            _buildReservationDetail(Icons.people, 'Guests', '${reservation.numberOfGuests}'),
            _buildReservationDetail(Icons.attach_money, 'Total', '\$${reservation.totalAmount.toStringAsFixed(2)}'),
            
            if (reservation.specialRequests != null) ...[
              const SizedBox(height: 8),
              _buildReservationDetail(Icons.note, 'Special Requests', reservation.specialRequests!),
            ],
            
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _generateReceipt(reservation),
                    icon: const Icon(Icons.receipt),
                    label: const Text('Generate Receipt'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceiptCard(Receipt receipt) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Receipt #${receipt.receiptNumber}',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.deepBlue,
                  ),
                ),
                Text(
                  receipt.formattedAmount,
                  style: GoogleFonts.lato(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryGold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildReceiptDetail('Payment Method', receipt.paymentMethod.displayName),
            _buildReceiptDetail('Payment Date', 
                '${receipt.paymentDate.day}/${receipt.paymentDate.month}/${receipt.paymentDate.year}'),
            if (receipt.description != null)
              _buildReceiptDetail('Description', receipt.description!),
            _buildReceiptDetail('Generated', 
                '${receipt.createdAt.day}/${receipt.createdAt.month}/${receipt.createdAt.year}'),
          ],
        ),
      ),
    );
  }

  Widget _buildReservationDetail(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppTheme.grey,
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: GoogleFonts.lato(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.grey,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.lato(
                fontSize: 14,
                color: AppTheme.deepBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.lato(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.grey,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.lato(
              fontSize: 14,
              color: AppTheme.deepBlue,
            ),
          ),
        ],
      ),
    );
  }
}
