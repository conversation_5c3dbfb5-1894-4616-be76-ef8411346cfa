# Hotel Reservation App 🏨

A comprehensive Flutter mobile application for hotel reservations with modern UI, SQLite database, notifications, SMS, API integration, and Bluetooth support.

## ✨ Features

### 🔐 Authentication & User Management
- **SplashScreen**: Animated splash screen with hotel branding
- **LoginActivity**: User authentication with internet and SQLite DB connectivity checks
- **SignUpActivity**: User registration with comprehensive form validation
- **ResetPasswordActivity**: Password recovery functionality
- **AccountActivity**: Profile management with editable user information

### 🏨 Hotel Booking System
- **MainActivity**: Hotel booking interface with date/time pickers
- **DashboardActivity**: Main navigation hub with bottom navigation
- **ReportActivity**: Booking history and receipt management
- **Real-time hotel data**: API integration for fetching hotel information

### 📱 Communication Features
- **Push Notifications**: Booking confirmations and reminders
- **SMS Integration**: Send SMS confirmations and updates
- **Phone Calls**: Direct calling from ContactActivity
- **Email Support**: Integrated email functionality

### 🔧 Advanced Features
- **SQLite Database**: Local storage for users, reservations, and receipts
- **Bluetooth Support**: Device connectivity and data sharing
- **Internet Connectivity**: Real-time connection monitoring
- **Modern UI**: Hotel-themed design with gold and blue color scheme
- **Settings Management**: Comprehensive app configuration

## 🎨 Design & UI

### Color Scheme
- **Primary Gold**: #D4AF37 (Luxury hotel aesthetic)
- **Deep Blue**: #1A237E (Professional contrast)
- **Cream Background**: #FFF8E1 (Warm, welcoming feel)
- **Modern Typography**: Playfair Display + Lato fonts

### Navigation
- **Bottom Navigation**: Easy access to main features
- **Drawer Navigation**: Alternative navigation pattern
- **Intuitive Flow**: User-friendly screen transitions

## 🛠 Technical Stack

### Dependencies
```yaml
dependencies:
  flutter: sdk
  sqflite: ^2.3.0                    # SQLite database
  http: ^1.1.0                       # API requests
  connectivity_plus: ^5.0.2          # Internet connectivity
  flutter_local_notifications: ^16.3.0 # Push notifications
  url_launcher: ^6.2.2               # Phone/email/web links
  permission_handler: ^11.1.0        # App permissions
  flutter_bluetooth_serial: ^0.4.0   # Bluetooth connectivity
  sms_advanced: ^1.0.1               # SMS functionality
  google_fonts: ^6.1.0               # Typography
  provider: ^6.1.1                   # State management
  shared_preferences: ^2.2.2         # Local preferences
  flutter_datetime_picker_plus: ^2.1.0 # Date/time selection
```

### Architecture
- **MVC Pattern**: Clean separation of concerns
- **Service Layer**: Database, API, Notifications, Bluetooth
- **Model Classes**: User, Reservation, Receipt, Hotel
- **Utility Classes**: Themes, Constants, Permissions

## 📁 Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   ├── user.dart
│   ├── reservation.dart
│   ├── receipt.dart
│   └── hotel.dart
├── screens/                  # UI screens
│   ├── splash_screen.dart
│   ├── login_screen.dart
│   ├── signup_screen.dart
│   ├── dashboard_screen.dart
│   ├── main_screen.dart
│   ├── account_screen.dart
│   ├── contact_screen.dart
│   ├── settings_screen.dart
│   ├── report_screen.dart
│   └── reset_password_screen.dart
├── services/                 # Business logic
│   ├── database_helper.dart
│   ├── notification_service.dart
│   ├── api_service.dart
│   ├── connectivity_service.dart
│   └── bluetooth_service.dart
├── utils/                    # Utilities
│   ├── app_theme.dart
│   ├── app_constants.dart
│   └── permissions_helper.dart
└── widgets/                  # Reusable components
    └── hotel_card.dart
```

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Android Studio / VS Code
- Android/iOS device or emulator

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd hotel_reservation_app
```

2. **Install dependencies**
```bash
flutter pub get
```

3. **Run the app**
```bash
flutter run
```

### Permissions Setup

The app requires several permissions for full functionality:

#### Android (android/app/src/main/AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.SEND_SMS" />
<uses-permission android:name="android.permission.CALL_PHONE" />
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
```

#### iOS (ios/Runner/Info.plist)
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs location access to find nearby hotels.</string>
<key>NSBluetoothAlwaysUsageDescription</key>
<string>This app uses Bluetooth to connect with nearby devices.</string>
<key>NSContactsUsageDescription</key>
<string>This app needs access to contacts for booking information.</string>
```

## 📱 App Screens

### 1. Splash Screen
- Animated hotel logo and branding
- Loading indicator
- Automatic navigation to login

### 2. Authentication Flow
- **Login**: Email/password with validation
- **Sign Up**: Comprehensive registration form
- **Reset Password**: Email-based recovery

### 3. Main Dashboard
- Bottom navigation with 5 tabs:
  - Home (Booking)
  - Reports (History)
  - Contact (Support)
  - Account (Profile)
  - Settings (Configuration)

### 4. Booking System
- Hotel selection with API data
- Room type and pricing
- Date/time pickers for check-in/out
- Guest count selection
- Special requests
- Real-time total calculation

### 5. Communication Features
- **Contact Screen**: Phone, SMS, Email integration
- **Notifications**: Booking confirmations and reminders
- **Emergency Contact**: 24/7 hotline access

### 6. Data Management
- **Reports**: Booking history and receipts
- **Account**: Profile editing and management
- **Settings**: App preferences and permissions

## 🔧 Configuration

### Database Schema
The app uses SQLite with three main tables:
- **users**: User authentication and profile data
- **reservations**: Booking information and status
- **receipts**: Payment records and transaction history

### API Integration
- Uses JSONPlaceholder for demo hotel data
- Supports real hotel API integration
- Includes error handling and offline support

### Notifications
- Local notifications for booking reminders
- Push notifications for confirmations
- Scheduled notifications for check-in alerts

## 🎯 Key Features Implemented

✅ **Complete Authentication System**
✅ **SQLite Database Integration**
✅ **Modern Hotel-Themed UI**
✅ **Bottom Navigation Structure**
✅ **Date/Time Pickers for Booking**
✅ **Push Notifications**
✅ **SMS Integration**
✅ **Phone Call Functionality**
✅ **API Integration**
✅ **Bluetooth Support**
✅ **Internet Connectivity Checks**
✅ **Comprehensive Settings**
✅ **Receipt Generation**
✅ **Form Validation**
✅ **Error Handling**

## 🔮 Future Enhancements

- [ ] Real hotel booking API integration
- [ ] Payment gateway integration
- [ ] Google Maps integration
- [ ] Social media login
- [ ] Multi-language support
- [ ] Dark mode theme
- [ ] Offline booking capability
- [ ] QR code scanning
- [ ] Loyalty program features
- [ ] Advanced search filters

## 📞 Support

For support and inquiries:
- **Email**: <EMAIL>
- **Phone**: +****************
- **Emergency**: +****************
- **Website**: www.hotelreservation.com

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👥 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

---

**Hotel Reservation App** - *Luxury at Your Fingertips* 🏨✨
