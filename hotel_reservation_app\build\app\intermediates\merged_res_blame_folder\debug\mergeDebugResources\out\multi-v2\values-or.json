{"logs": [{"outputFile": "com.example.hotel_reservation_app-mergeDebugResources-41:/values-or/values-or.xml", "map": [{"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2850,2953,3055,3158,3263,3364,3466,4487", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "2948,3050,3153,3258,3359,3461,3580,4583"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\2a5e79e83ba32f0781622d43a41ff54b\\transformed\\appcompat-1.3.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,906,997,1090,1186,1281,1381,1474,1569,1665,1756,1846,1935,2045,2149,2248,2359,2463,2581,2744,2850", "endColumns": "118,109,106,85,103,119,78,75,90,92,95,94,99,92,94,95,90,89,88,109,103,98,110,103,117,162,105,82", "endOffsets": "219,329,436,522,626,746,825,901,992,1085,1181,1276,1376,1469,1564,1660,1751,1841,1930,2040,2144,2243,2354,2458,2576,2739,2845,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,906,997,1090,1186,1281,1381,1474,1569,1665,1756,1846,1935,2045,2149,2248,2359,2463,2581,2744,4404", "endColumns": "118,109,106,85,103,119,78,75,90,92,95,94,99,92,94,95,90,89,88,109,103,98,110,103,117,162,105,82", "endOffsets": "219,329,436,522,626,746,825,901,992,1085,1181,1276,1376,1469,1564,1660,1751,1841,1930,2040,2144,2243,2354,2458,2576,2739,2845,4482"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3659,3858,3963,4076", "endColumns": "109,104,112,108", "endOffsets": "3764,3958,4071,4180"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,268,348,487,656,737", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "174,263,343,482,651,732,814"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3585,3769,4185,4265,4588,4757,4838", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "3654,3853,4260,4399,4752,4833,4915"}}]}]}