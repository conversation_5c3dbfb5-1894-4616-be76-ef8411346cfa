import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_theme.dart';

class PermissionsHelper {
  static Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    return status.isGranted;
  }

  static Future<bool> requestSMSPermission() async {
    final status = await Permission.sms.request();
    return status.isGranted;
  }

  static Future<bool> requestPhonePermission() async {
    final status = await Permission.phone.request();
    return status.isGranted;
  }

  static Future<bool> requestBluetoothPermissions() async {
    final Map<Permission, PermissionStatus> statuses = await [
      Permission.bluetooth,
      Permission.bluetoothConnect,
      Permission.bluetoothScan,
      Permission.location,
    ].request();

    return statuses.values.every((status) => status.isGranted);
  }

  static Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status.isGranted;
  }

  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  static Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  static Future<Map<String, bool>> requestAllPermissions() async {
    final Map<Permission, PermissionStatus> statuses = await [
      Permission.notification,
      Permission.sms,
      Permission.phone,
      Permission.bluetooth,
      Permission.bluetoothConnect,
      Permission.bluetoothScan,
      Permission.location,
      Permission.camera,
      Permission.storage,
    ].request();

    return {
      'notification': statuses[Permission.notification]?.isGranted ?? false,
      'sms': statuses[Permission.sms]?.isGranted ?? false,
      'phone': statuses[Permission.phone]?.isGranted ?? false,
      'bluetooth': statuses[Permission.bluetooth]?.isGranted ?? false,
      'bluetoothConnect': statuses[Permission.bluetoothConnect]?.isGranted ?? false,
      'bluetoothScan': statuses[Permission.bluetoothScan]?.isGranted ?? false,
      'location': statuses[Permission.location]?.isGranted ?? false,
      'camera': statuses[Permission.camera]?.isGranted ?? false,
      'storage': statuses[Permission.storage]?.isGranted ?? false,
    };
  }

  static Future<bool> checkPermissionStatus(Permission permission) async {
    final status = await permission.status;
    return status.isGranted;
  }

  static Future<void> showPermissionDialog({
    required BuildContext context,
    required String title,
    required String message,
    required VoidCallback onGranted,
    VoidCallback? onDenied,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: GoogleFonts.playfairDisplay(
              fontWeight: FontWeight.w600,
              color: AppTheme.deepBlue,
            ),
          ),
          content: Text(
            message,
            style: GoogleFonts.lato(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDenied?.call();
              },
              child: Text(
                'Deny',
                style: GoogleFonts.lato(color: AppTheme.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onGranted();
              },
              child: Text(
                'Grant',
                style: GoogleFonts.lato(),
              ),
            ),
          ],
        );
      },
    );
  }

  static Future<void> showSettingsDialog({
    required BuildContext context,
    required String title,
    required String message,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: GoogleFonts.playfairDisplay(
              fontWeight: FontWeight.w600,
              color: AppTheme.deepBlue,
            ),
          ),
          content: Text(
            message,
            style: GoogleFonts.lato(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: GoogleFonts.lato(color: AppTheme.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: Text(
                'Open Settings',
                style: GoogleFonts.lato(),
              ),
            ),
          ],
        );
      },
    );
  }

  static String getPermissionDescription(String permissionName) {
    switch (permissionName) {
      case 'notification':
        return 'Receive booking confirmations and reminders';
      case 'sms':
        return 'Send SMS confirmations and updates';
      case 'phone':
        return 'Make phone calls to hotel support';
      case 'bluetooth':
      case 'bluetoothConnect':
      case 'bluetoothScan':
        return 'Connect to nearby devices and share booking information';
      case 'location':
        return 'Find nearby hotels and provide location-based services';
      case 'camera':
        return 'Scan QR codes and take photos for bookings';
      case 'storage':
        return 'Save booking documents and receipts';
      default:
        return 'Required for app functionality';
    }
  }

  static IconData getPermissionIcon(String permissionName) {
    switch (permissionName) {
      case 'notification':
        return Icons.notifications;
      case 'sms':
        return Icons.sms;
      case 'phone':
        return Icons.phone;
      case 'bluetooth':
      case 'bluetoothConnect':
      case 'bluetoothScan':
        return Icons.bluetooth;
      case 'location':
        return Icons.location_on;
      case 'camera':
        return Icons.camera_alt;
      case 'storage':
        return Icons.storage;
      default:
        return Icons.security;
    }
  }

  static Color getPermissionColor(bool isGranted) {
    return isGranted ? Colors.green : Colors.red;
  }
}
