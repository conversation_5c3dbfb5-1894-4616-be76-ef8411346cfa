{"inputs": ["D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\.dart_tool\\flutter_build\\dcc94dc32b29e3b0164ab048fab1285b\\app.dill", "D:\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\flutter\\bin\\cache\\engine.stamp", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\pubspec.yaml", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\assets\\icons\\app_icon.png", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "D:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "D:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\.dart_tool\\flutter_build\\dcc94dc32b29e3b0164ab048fab1285b\\native_assets.json", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\archive-4.0.7\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\checked_yaml-2.0.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\cli_util-0.4.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\connectivity_plus-5.0.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\connectivity_plus_platform_interface-1.2.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\dbus-0.7.11\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_bluetooth_serial-0.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_datetime_picker_plus-2.2.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_launcher_icons-0.13.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_local_notifications-16.3.3\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_local_notifications_linux-4.0.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-7.2.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\google_fonts-6.2.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\image-4.5.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\js-0.6.7\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\nm-0.5.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\posix-6.0.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\timezone-0.9.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "D:\\FlutterCache\\PubCache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE", "D:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "D:\\flutter\\packages\\flutter\\LICENSE", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD737111047"], "outputs": ["D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\app_icon.png", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\HOTEL RESERVATION APP\\hotel_reservation_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}