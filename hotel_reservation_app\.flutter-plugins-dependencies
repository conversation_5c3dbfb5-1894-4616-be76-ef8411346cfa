{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "connectivity_plus", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-16.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.7\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "connectivity_plus", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_bluetooth_serial", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\flutter_bluetooth_serial-0.4.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-16.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.10\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.3.16\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "connectivity_plus", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications-16.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "connectivity_plus", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\flutter_local_notifications_linux-4.0.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "connectivity_plus", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "connectivity_plus", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-5.0.2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "D:\\\\FlutterCache\\\\PubCache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.4.1\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "connectivity_plus", "dependencies": []}, {"name": "flutter_bluetooth_serial", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-06-25 01:20:25.822951", "version": "3.32.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}