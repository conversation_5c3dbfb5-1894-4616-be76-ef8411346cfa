class UserFields {
  static final List<String> values = [
    id, email, password, firstName, lastName, phone, address, dateOfBirth, createdAt, updatedAt
  ];

  static const String tableName = 'users';
  static const String id = '_id';
  static const String email = 'email';
  static const String password = 'password';
  static const String firstName = 'first_name';
  static const String lastName = 'last_name';
  static const String phone = 'phone';
  static const String address = 'address';
  static const String dateOfBirth = 'date_of_birth';
  static const String createdAt = 'created_at';
  static const String updatedAt = 'updated_at';
}

class User {
  final int? id;
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String? phone;
  final String? address;
  final String? dateOfBirth;
  final DateTime createdAt;
  final DateTime updatedAt;

  const User({
    this.id,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.address,
    this.dateOfBirth,
    required this.createdAt,
    required this.updatedAt,
  });

  User copy({
    int? id,
    String? email,
    String? password,
    String? firstName,
    String? lastName,
    String? phone,
    String? address,
    String? dateOfBirth,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      User(
        id: id ?? this.id,
        email: email ?? this.email,
        password: password ?? this.password,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        phone: phone ?? this.phone,
        address: address ?? this.address,
        dateOfBirth: dateOfBirth ?? this.dateOfBirth,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  static User fromJson(Map<String, Object?> json) => User(
        id: json[UserFields.id] as int?,
        email: json[UserFields.email] as String,
        password: json[UserFields.password] as String,
        firstName: json[UserFields.firstName] as String,
        lastName: json[UserFields.lastName] as String,
        phone: json[UserFields.phone] as String?,
        address: json[UserFields.address] as String?,
        dateOfBirth: json[UserFields.dateOfBirth] as String?,
        createdAt: DateTime.parse(json[UserFields.createdAt] as String),
        updatedAt: DateTime.parse(json[UserFields.updatedAt] as String),
      );

  Map<String, Object?> toJson() => {
        UserFields.id: id,
        UserFields.email: email,
        UserFields.password: password,
        UserFields.firstName: firstName,
        UserFields.lastName: lastName,
        UserFields.phone: phone,
        UserFields.address: address,
        UserFields.dateOfBirth: dateOfBirth,
        UserFields.createdAt: createdAt.toIso8601String(),
        UserFields.updatedAt: updatedAt.toIso8601String(),
      };

  String get fullName => '$firstName $lastName';

  @override
  String toString() {
    return 'User{id: $id, email: $email, firstName: $firstName, lastName: $lastName, phone: $phone, address: $address, dateOfBirth: $dateOfBirth, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          email == other.email;

  @override
  int get hashCode => id.hashCode ^ email.hashCode;
}
