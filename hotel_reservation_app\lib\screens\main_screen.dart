import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/app_theme.dart';
import '../models/user.dart';
import '../models/reservation.dart';
import '../models/hotel.dart';
import '../services/database_helper.dart';
import '../services/api_service.dart';

class MainScreen extends StatefulWidget {
  final User user;

  const MainScreen({super.key, required this.user});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final _formKey = GlobalKey<FormState>();
  final _hotelNameController = TextEditingController();
  final _specialRequestsController = TextEditingController();

  DateTime? _checkInDate;
  DateTime? _checkOutDate;
  String _selectedRoomType = 'Standard Room';
  int _numberOfGuests = 1;
  bool _isLoading = false;
  List<Hotel> _availableHotels = [];
  bool _loadingHotels = false;

  final List<String> _roomTypes = [
    'Standard Room',
    'Deluxe Room',
    'Suite',
    'Presidential Suite',
    'Family Room',
    'Business Room',
  ];

  final Map<String, double> _roomPrices = {
    'Standard Room': 100.0,
    'Deluxe Room': 150.0,
    'Suite': 250.0,
    'Presidential Suite': 500.0,
    'Family Room': 180.0,
    'Business Room': 200.0,
  };

  @override
  void initState() {
    super.initState();
    _loadAvailableHotels();
  }

  @override
  void dispose() {
    _hotelNameController.dispose();
    _specialRequestsController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableHotels() async {
    setState(() {
      _loadingHotels = true;
    });

    try {
      final hotels = await ApiService.fetchHotels();
      setState(() {
        _availableHotels = hotels;
        _loadingHotels = false;
      });
    } catch (e) {
      setState(() {
        _loadingHotels = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading hotels: $e'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  double get _totalAmount {
    if (_checkInDate == null || _checkOutDate == null) return 0.0;
    final nights = _checkOutDate!.difference(_checkInDate!).inDays;
    return (_roomPrices[_selectedRoomType] ?? 0.0) * nights;
  }

  Future<void> _selectCheckInDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _checkInDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryGold,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _checkInDate) {
      setState(() {
        _checkInDate = picked;
        // Reset checkout date if it's before checkin date
        if (_checkOutDate != null && _checkOutDate!.isBefore(picked)) {
          _checkOutDate = null;
        }
      });
    }
  }

  Future<void> _selectCheckOutDate() async {
    if (_checkInDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select check-in date first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _checkOutDate ?? _checkInDate!.add(const Duration(days: 1)),
      firstDate: _checkInDate!.add(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryGold,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _checkOutDate) {
      setState(() {
        _checkOutDate = picked;
      });
    }
  }

  Future<void> _makeReservation() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (_checkInDate == null || _checkOutDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select check-in and check-out dates'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final reservation = Reservation(
        userId: widget.user.id!,
        hotelName: _hotelNameController.text.trim(),
        roomType: _selectedRoomType,
        checkInDate: _checkInDate!,
        checkOutDate: _checkOutDate!,
        numberOfGuests: _numberOfGuests,
        totalAmount: _totalAmount,
        status: ReservationStatus.pending,
        specialRequests: _specialRequestsController.text.trim().isEmpty 
            ? null 
            : _specialRequestsController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final createdReservation = await DatabaseHelper.instance.createReservation(reservation);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reservation created successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Clear form
        _hotelNameController.clear();
        _specialRequestsController.clear();
        setState(() {
          _checkInDate = null;
          _checkOutDate = null;
          _selectedRoomType = 'Standard Room';
          _numberOfGuests = 1;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating reservation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.cream,
      appBar: AppBar(
        title: Text(
          'Book Your Stay',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.primaryGold,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Welcome message
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Welcome, ${widget.user.firstName}!',
                        style: GoogleFonts.playfairDisplay(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.deepBlue,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Book your perfect hotel stay',
                        style: GoogleFonts.lato(
                          fontSize: 16,
                          color: AppTheme.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // Hotel Name
                TextFormField(
                  controller: _hotelNameController,
                  decoration: const InputDecoration(
                    labelText: 'Hotel Name',
                    prefixIcon: Icon(Icons.hotel),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter hotel name';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 20),
                
                // Room Type
                DropdownButtonFormField<String>(
                  value: _selectedRoomType,
                  decoration: const InputDecoration(
                    labelText: 'Room Type',
                    prefixIcon: Icon(Icons.bed),
                  ),
                  items: _roomTypes.map((String roomType) {
                    return DropdownMenuItem<String>(
                      value: roomType,
                      child: Text('$roomType - \$${_roomPrices[roomType]}/night'),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedRoomType = newValue;
                      });
                    }
                  },
                ),
                
                const SizedBox(height: 20),
                
                // Check-in Date
                InkWell(
                  onTap: _selectCheckInDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Check-in Date',
                      prefixIcon: Icon(Icons.calendar_today),
                      suffixIcon: Icon(Icons.arrow_drop_down),
                    ),
                    child: Text(
                      _checkInDate != null
                          ? '${_checkInDate!.day}/${_checkInDate!.month}/${_checkInDate!.year}'
                          : 'Select check-in date',
                      style: GoogleFonts.lato(
                        color: _checkInDate != null ? Colors.black : AppTheme.grey,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Check-out Date
                InkWell(
                  onTap: _selectCheckOutDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Check-out Date',
                      prefixIcon: Icon(Icons.calendar_today),
                      suffixIcon: Icon(Icons.arrow_drop_down),
                    ),
                    child: Text(
                      _checkOutDate != null
                          ? '${_checkOutDate!.day}/${_checkOutDate!.month}/${_checkOutDate!.year}'
                          : 'Select check-out date',
                      style: GoogleFonts.lato(
                        color: _checkOutDate != null ? Colors.black : AppTheme.grey,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Number of Guests
                Row(
                  children: [
                    const Icon(Icons.people, color: AppTheme.grey),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Number of Guests',
                        style: GoogleFonts.lato(
                          fontSize: 16,
                          color: AppTheme.grey,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _numberOfGuests > 1
                          ? () => setState(() => _numberOfGuests--)
                          : null,
                      icon: const Icon(Icons.remove),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppTheme.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '$_numberOfGuests',
                        style: GoogleFonts.lato(fontSize: 16),
                      ),
                    ),
                    IconButton(
                      onPressed: _numberOfGuests < 10
                          ? () => setState(() => _numberOfGuests++)
                          : null,
                      icon: const Icon(Icons.add),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Special Requests
                TextFormField(
                  controller: _specialRequestsController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Special Requests (Optional)',
                    prefixIcon: Icon(Icons.note_add),
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // Total Amount
                if (_checkInDate != null && _checkOutDate != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGold.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppTheme.primaryGold),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Total Amount',
                          style: GoogleFonts.lato(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.deepBlue,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '\$${_totalAmount.toStringAsFixed(2)}',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryGold,
                          ),
                        ),
                        Text(
                          '${_checkOutDate!.difference(_checkInDate!).inDays} nights',
                          style: GoogleFonts.lato(
                            fontSize: 14,
                            color: AppTheme.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                
                const SizedBox(height: 30),
                
                // Book Now Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _makeReservation,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Book Now'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
