import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/hotel.dart';

class ApiService {
  static const String _baseUrl = 'https://jsonplaceholder.typicode.com';
  static const Duration _timeout = Duration(seconds: 30);

  static Future<bool> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  static Future<List<Hotel>> fetchHotels() async {
    if (!await _checkConnectivity()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/posts'),
            headers: {
              'Content-Type': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        
        // Transform the placeholder data into hotel data
        return jsonData.take(10).map((item) {
          return Hotel.fromPlaceholderData(item);
        }).toList();
      } else {
        throw Exception('Failed to load hotels: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching hotels: $e');
    }
  }

  static Future<Hotel> fetchHotelById(int id) async {
    if (!await _checkConnectivity()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/posts/$id'),
            headers: {
              'Content-Type': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return Hotel.fromPlaceholderData(jsonData);
      } else {
        throw Exception('Failed to load hotel: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching hotel: $e');
    }
  }

  static Future<List<String>> fetchHotelAmenities(int hotelId) async {
    if (!await _checkConnectivity()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/posts/$hotelId/comments'),
            headers: {
              'Content-Type': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        
        // Transform comments into amenities
        return jsonData.take(5).map((comment) {
          return _generateAmenityFromComment(comment['name'] ?? 'Amenity');
        }).toList();
      } else {
        throw Exception('Failed to load amenities: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching amenities: $e');
    }
  }

  static String _generateAmenityFromComment(String commentName) {
    final amenities = [
      'Free WiFi',
      'Swimming Pool',
      'Fitness Center',
      'Spa Services',
      'Restaurant',
      'Room Service',
      'Parking',
      'Business Center',
      'Concierge',
      'Laundry Service',
    ];
    
    final hash = commentName.hashCode.abs();
    return amenities[hash % amenities.length];
  }

  static Future<Map<String, dynamic>> fetchWeatherData(String city) async {
    if (!await _checkConnectivity()) {
      throw Exception('No internet connection');
    }

    try {
      // Using a mock weather API response since we're using placeholder data
      final response = await http
          .get(
            Uri.parse('$_baseUrl/users/1'),
            headers: {
              'Content-Type': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        // Generate mock weather data
        return {
          'city': city,
          'temperature': 22 + (city.hashCode.abs() % 15), // 22-37°C
          'condition': _getWeatherCondition(city.hashCode.abs() % 4),
          'humidity': 40 + (city.hashCode.abs() % 40), // 40-80%
          'windSpeed': 5 + (city.hashCode.abs() % 15), // 5-20 km/h
        };
      } else {
        throw Exception('Failed to load weather data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching weather data: $e');
    }
  }

  static String _getWeatherCondition(int index) {
    const conditions = ['Sunny', 'Cloudy', 'Rainy', 'Partly Cloudy'];
    return conditions[index];
  }

  static Future<List<Map<String, dynamic>>> fetchNearbyAttractions(String city) async {
    if (!await _checkConnectivity()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/albums'),
            headers: {
              'Content-Type': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        
        return jsonData.take(5).map((album) {
          return {
            'name': _generateAttractionName(album['title']),
            'description': 'Popular attraction in $city',
            'rating': 3.5 + ((album['id'] as int) % 3) * 0.5, // 3.5-5.0
            'distance': '${1 + ((album['id'] as int) % 10)} km',
          };
        }).toList();
      } else {
        throw Exception('Failed to load attractions: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching attractions: $e');
    }
  }

  static String _generateAttractionName(String albumTitle) {
    final attractions = [
      'Historic Downtown',
      'City Museum',
      'Central Park',
      'Art Gallery',
      'Shopping District',
      'Waterfront',
      'Cultural Center',
      'Observatory',
      'Botanical Garden',
      'Local Market',
    ];
    
    final hash = albumTitle.hashCode.abs();
    return attractions[hash % attractions.length];
  }

  static Future<Map<String, dynamic>> submitFeedback({
    required String name,
    required String email,
    required String message,
    required int rating,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/posts'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: json.encode({
              'title': 'Feedback from $name',
              'body': message,
              'userId': 1,
            }),
          )
          .timeout(_timeout);

      if (response.statusCode == 201) {
        return {
          'success': true,
          'message': 'Feedback submitted successfully',
          'id': json.decode(response.body)['id'],
        };
      } else {
        throw Exception('Failed to submit feedback: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error submitting feedback: $e');
    }
  }

  static Future<List<Map<String, dynamic>>> fetchPromotions() async {
    if (!await _checkConnectivity()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl/photos'),
            headers: {
              'Content-Type': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        
        return jsonData.take(3).map((photo) {
          return {
            'title': _generatePromotionTitle(photo['title']),
            'description': 'Special offer for hotel bookings',
            'discount': '${10 + ((photo['id'] as int) % 40)}%', // 10-50%
            'validUntil': DateTime.now().add(Duration(days: 7 + ((photo['id'] as int) % 23))).toIso8601String(),
            'imageUrl': photo['thumbnailUrl'],
          };
        }).toList();
      } else {
        throw Exception('Failed to load promotions: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching promotions: $e');
    }
  }

  static String _generatePromotionTitle(String photoTitle) {
    final promotions = [
      'Early Bird Special',
      'Weekend Getaway',
      'Extended Stay Discount',
      'Family Package',
      'Business Traveler Deal',
      'Last Minute Offer',
      'Seasonal Promotion',
      'Loyalty Reward',
    ];
    
    final hash = photoTitle.hashCode.abs();
    return promotions[hash % promotions.length];
  }
}
