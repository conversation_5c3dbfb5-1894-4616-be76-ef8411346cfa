class ReceiptFields {
  static final List<String> values = [
    id, reservationId, receiptNumber, amount, paymentMethod, paymentDate, description, createdAt
  ];

  static const String tableName = 'receipts';
  static const String id = '_id';
  static const String reservationId = 'reservation_id';
  static const String receiptNumber = 'receipt_number';
  static const String amount = 'amount';
  static const String paymentMethod = 'payment_method';
  static const String paymentDate = 'payment_date';
  static const String description = 'description';
  static const String createdAt = 'created_at';
}

enum PaymentMethod {
  cash,
  creditCard,
  debitCard,
  bankTransfer,
  digitalWallet,
  other,
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.digitalWallet:
        return 'Digital Wallet';
      case PaymentMethod.other:
        return 'Other';
    }
  }

  String get value {
    switch (this) {
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.creditCard:
        return 'credit_card';
      case PaymentMethod.debitCard:
        return 'debit_card';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
      case PaymentMethod.digitalWallet:
        return 'digital_wallet';
      case PaymentMethod.other:
        return 'other';
    }
  }

  static PaymentMethod fromString(String method) {
    switch (method) {
      case 'cash':
        return PaymentMethod.cash;
      case 'credit_card':
        return PaymentMethod.creditCard;
      case 'debit_card':
        return PaymentMethod.debitCard;
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      case 'digital_wallet':
        return PaymentMethod.digitalWallet;
      case 'other':
        return PaymentMethod.other;
      default:
        return PaymentMethod.other;
    }
  }
}

class Receipt {
  final int? id;
  final int reservationId;
  final String receiptNumber;
  final double amount;
  final PaymentMethod paymentMethod;
  final DateTime paymentDate;
  final String? description;
  final DateTime createdAt;

  const Receipt({
    this.id,
    required this.reservationId,
    required this.receiptNumber,
    required this.amount,
    required this.paymentMethod,
    required this.paymentDate,
    this.description,
    required this.createdAt,
  });

  Receipt copy({
    int? id,
    int? reservationId,
    String? receiptNumber,
    double? amount,
    PaymentMethod? paymentMethod,
    DateTime? paymentDate,
    String? description,
    DateTime? createdAt,
  }) =>
      Receipt(
        id: id ?? this.id,
        reservationId: reservationId ?? this.reservationId,
        receiptNumber: receiptNumber ?? this.receiptNumber,
        amount: amount ?? this.amount,
        paymentMethod: paymentMethod ?? this.paymentMethod,
        paymentDate: paymentDate ?? this.paymentDate,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
      );

  static Receipt fromJson(Map<String, Object?> json) => Receipt(
        id: json[ReceiptFields.id] as int?,
        reservationId: json[ReceiptFields.reservationId] as int,
        receiptNumber: json[ReceiptFields.receiptNumber] as String,
        amount: json[ReceiptFields.amount] as double,
        paymentMethod: PaymentMethodExtension.fromString(json[ReceiptFields.paymentMethod] as String),
        paymentDate: DateTime.parse(json[ReceiptFields.paymentDate] as String),
        description: json[ReceiptFields.description] as String?,
        createdAt: DateTime.parse(json[ReceiptFields.createdAt] as String),
      );

  Map<String, Object?> toJson() => {
        ReceiptFields.id: id,
        ReceiptFields.reservationId: reservationId,
        ReceiptFields.receiptNumber: receiptNumber,
        ReceiptFields.amount: amount,
        ReceiptFields.paymentMethod: paymentMethod.value,
        ReceiptFields.paymentDate: paymentDate.toIso8601String(),
        ReceiptFields.description: description,
        ReceiptFields.createdAt: createdAt.toIso8601String(),
      };

  String get formattedAmount {
    return '\$${amount.toStringAsFixed(2)}';
  }

  @override
  String toString() {
    return 'Receipt{id: $id, reservationId: $reservationId, receiptNumber: $receiptNumber, amount: $amount, paymentMethod: $paymentMethod, paymentDate: $paymentDate, description: $description, createdAt: $createdAt}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Receipt &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
