class AppConstants {
  // App Information
  static const String appName = 'Hotel Reservation';
  static const String appVersion = '1.0.0';
  static const String appBuild = '1';
  static const String appDescription = 'Luxury hotel reservations at your fingertips';

  // Contact Information
  static const String supportPhone = '+****************';
  static const String emergencyPhone = '+****************';
  static const String supportEmail = '<EMAIL>';
  static const String supportWebsite = 'www.hotelreservation.com';

  // API Configuration
  static const String baseApiUrl = 'https://jsonplaceholder.typicode.com';
  static const Duration apiTimeout = Duration(seconds: 30);

  // Database Configuration
  static const String databaseName = 'hotel_reservation.db';
  static const int databaseVersion = 1;

  // Notification Channels
  static const String bookingChannelId = 'booking_channel';
  static const String bookingChannelName = 'Booking Notifications';
  static const String bookingChannelDescription = 'Notifications for hotel bookings';

  static const String reminderChannelId = 'reminder_channel';
  static const String reminderChannelName = 'Booking Reminders';
  static const String reminderChannelDescription = 'Reminders for upcoming bookings';

  static const String paymentChannelId = 'payment_channel';
  static const String paymentChannelName = 'Payment Notifications';
  static const String paymentChannelDescription = 'Notifications for payments';

  static const String generalChannelId = 'general_channel';
  static const String generalChannelName = 'General Notifications';
  static const String generalChannelDescription = 'General app notifications';

  // Room Types and Prices
  static const Map<String, double> roomPrices = {
    'Standard Room': 100.0,
    'Deluxe Room': 150.0,
    'Suite': 250.0,
    'Presidential Suite': 500.0,
    'Family Room': 180.0,
    'Business Room': 200.0,
  };

  static const List<String> roomTypes = [
    'Standard Room',
    'Deluxe Room',
    'Suite',
    'Presidential Suite',
    'Family Room',
    'Business Room',
  ];

  // Hotel Amenities
  static const List<String> commonAmenities = [
    'Free WiFi',
    'Swimming Pool',
    'Fitness Center',
    'Spa Services',
    'Restaurant',
    'Room Service',
    'Parking',
    'Business Center',
    'Concierge',
    'Laundry Service',
    'Bar/Lounge',
    'Meeting Rooms',
    'Tennis Court',
    'Golf Course',
    'Beach Access',
    'Water Sports',
    'Kids Club',
    'Entertainment',
    'Airport Shuttle',
    'Pet Friendly',
  ];

  // Payment Methods
  static const List<String> paymentMethods = [
    'Cash',
    'Credit Card',
    'Debit Card',
    'Bank Transfer',
    'Digital Wallet',
    'Other',
  ];

  // Supported Languages
  static const List<String> supportedLanguages = [
    'English',
    'Spanish',
    'French',
    'German',
    'Italian',
    'Portuguese',
    'Chinese',
    'Japanese',
    'Korean',
    'Arabic',
  ];

  // Supported Currencies
  static const List<String> supportedCurrencies = [
    'USD',
    'EUR',
    'GBP',
    'CAD',
    'AUD',
    'JPY',
    'CNY',
    'INR',
    'BRL',
    'MXN',
  ];

  // Currency Symbols
  static const Map<String, String> currencySymbols = {
    'USD': '\$',
    'EUR': '€',
    'GBP': '£',
    'CAD': 'C\$',
    'AUD': 'A\$',
    'JPY': '¥',
    'CNY': '¥',
    'INR': '₹',
    'BRL': 'R\$',
    'MXN': '\$',
  };

  // Office Hours
  static const Map<String, String> officeHours = {
    'Monday': '9:00 AM - 8:00 PM',
    'Tuesday': '9:00 AM - 8:00 PM',
    'Wednesday': '9:00 AM - 8:00 PM',
    'Thursday': '9:00 AM - 8:00 PM',
    'Friday': '9:00 AM - 8:00 PM',
    'Saturday': '10:00 AM - 6:00 PM',
    'Sunday': '12:00 PM - 5:00 PM',
  };

  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int maxGuestsPerRoom = 10;
  static const int maxAdvanceBookingDays = 365;

  // Default Values
  static const int defaultNumberOfGuests = 1;
  static const String defaultRoomType = 'Standard Room';
  static const String defaultCurrency = 'USD';
  static const String defaultLanguage = 'English';

  // Error Messages
  static const String networkErrorMessage = 'Please check your internet connection and try again.';
  static const String databaseErrorMessage = 'Database error occurred. Please try again.';
  static const String permissionDeniedMessage = 'Permission denied. Please grant the required permissions.';
  static const String bluetoothNotAvailableMessage = 'Bluetooth is not available on this device.';
  static const String locationNotAvailableMessage = 'Location services are not available.';

  // Success Messages
  static const String bookingSuccessMessage = 'Booking created successfully!';
  static const String paymentSuccessMessage = 'Payment processed successfully!';
  static const String profileUpdatedMessage = 'Profile updated successfully!';
  static const String settingsSavedMessage = 'Settings saved successfully!';

  // Date Formats
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String apiDateFormat = 'yyyy-MM-dd';
  static const String timestampFormat = 'yyyy-MM-dd HH:mm:ss';

  // Image Placeholders
  static const String hotelPlaceholderImage = 'https://via.placeholder.com/400x300?text=Hotel';
  static const String userPlaceholderImage = 'https://via.placeholder.com/100x100?text=User';

  // Social Media Links
  static const String facebookUrl = 'https://facebook.com/hotelreservation';
  static const String twitterUrl = 'https://twitter.com/hotelreservation';
  static const String instagramUrl = 'https://instagram.com/hotelreservation';
  static const String linkedinUrl = 'https://linkedin.com/company/hotelreservation';

  // Terms and Privacy
  static const String termsOfServiceUrl = 'https://hotelreservation.com/terms';
  static const String privacyPolicyUrl = 'https://hotelreservation.com/privacy';
  static const String helpCenterUrl = 'https://hotelreservation.com/help';

  // Feature Flags
  static const bool enableBluetooth = true;
  static const bool enableNotifications = true;
  static const bool enableSMS = true;
  static const bool enableLocationServices = true;
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = false;
}
